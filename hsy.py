import plotly.graph_objects as go
import pandas as pd
from venom_sdk import venom
import datetime as dt
import urllib3
from datetime import datetime

urllib3.disable_warnings()
from src.utilities import vanilla_prediction_pull


def pull_sdk_lmps(node_name, shard_name, date):
    """Can pull lmps actuals or day ahead values for a specified node, returning data in a dataframe
        Args:
            node_name: str
            shard_name: str
            start_dt and end_dt: datetime (UTC)

        Returns:
            dataframe- cols: 'forecast_datetime' (now in UTC-5) and 'fcst@nodal@lmp@<node_name>@'
    """
    with venom.VenomClientService('https://venom.iuat.woodmac.com') as client:
        col_resp = client.query_column(
            request=venom.QueryColumnRequest(
                shard_name=shard_name
            )
        )

        node_cols = [col.name for col in col_resp.columns if node_name in col.name]

        if not node_cols:
            raise ValueError(f"No values found for node '{node_name}'.")

        query_resp = client.query(
            request=venom.QueryRequest(
                fields=node_cols,
                filters=[],
                start_date=date + dt.timedelta(hours=5),
                end_date=date + dt.timedelta(days=1) + dt.timedelta(hours=5)
            )
        )
    lmps_df = query_resp.to_dataframe(columns=node_cols)
    df_standardized = lmps_df.reset_index(names=["forecast_datetime"])
    df_standardized['forecast_datetime'] = df_standardized['forecast_datetime'].dt.tz_convert('Etc/GMT+5')
    return df_standardized

def pull_and_merge_forecasts_and_actuals(node_name, date):
    """
    Merges prediction and actual data for LMP, MCC, MEC, and DA LMPs.

    Args:
        predictions_df (pd.DataFrame): Forecast data with columns 'forecast_datetime', 'publication_datetime', 'lmp', 'mcc', 'mec', 'pred_range_LMP'.
        lmp_actuals_df (pd.DataFrame): Actual LMPs with columns ['forecast_datetime', 'actual_lmp'].
        lmp_da_df (pd.DataFrame): Day-ahead LMPs with columns ['forecast_datetime', 'da_lmp'].
        mcc_df (pd.DataFrame): Actual MCCs with columns ['forecast_datetime', 'actual_mcc'].

    Returns:
        pd.DataFrame: Fully merged DataFrame with all relevant prediction and actuals, plus 'node_name' column.
    """
    ### PULLS
    predictions_df = vanilla_prediction_pull(
        date_range=pd.date_range(end=date, periods=1, freq='D'),
        nodes=[node_name],
    )
    # Actuals pulls
    lmp_actuals_df = pull_sdk_lmps(node_name, "lmps actual", date)
    lmp_da_df = pull_sdk_lmps(node_name, "lmps forecast", date)
    mcc_df = pull_sdk_lmps(node_name, "mcc actual", date)

    ### MERGE
    # Convert forecast_datetime to tz-aware
    predictions_df['forecast_datetime'] = pd.to_datetime(predictions_df['forecast_datetime']).dt.tz_convert('Etc/GMT+5')

    # Rename columns
    predictions_df = predictions_df.rename(columns={
        'lmp': 'predicted_lmp',
        'mcc': 'predicted_mcc',
        'mec': 'predicted_mec'
    })[['forecast_datetime', 'publication_datetime', 'predicted_lmp', 'predicted_mcc', 'predicted_mec',
        'pred_range_LMP']]

    lmp_actuals_df = lmp_actuals_df.rename(columns={lmp_actuals_df.columns[1]: 'actual_lmp'})
    lmp_da_df = lmp_da_df.rename(columns={lmp_da_df.columns[1]: 'da_lmp'})
    mcc_df = mcc_df.rename(columns={mcc_df.columns[1]: 'actual_mcc'})

    # Merge all dataframes on forecast_datetime
    merged_df = predictions_df.merge(lmp_actuals_df, on='forecast_datetime', how='inner')
    merged_df = merged_df.merge(mcc_df, on='forecast_datetime', how='inner')
    merged_df = merged_df.merge(lmp_da_df, on='forecast_datetime', how='inner')

    # Add node_name column with constant value
    merged_df['node_name'] = node_name
    cols = merged_df.columns.tolist()
    cols = ['node_name'] + [c for c in cols if c != 'node_name']
    merged_df = merged_df[cols]

    return merged_df

def calculate_actual_mec(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculates actual MEC from actual LMP and actual MCC.
    Adds 'actual_mec' column to the input DataFrame.

    Args:
        df (pd.DataFrame): DataFrame with 'actual_lmp' and 'actual_mcc'.

    Returns:
        pd.DataFrame: DataFrame with added 'actual_mec' column.
    """
    df['actual_mec'] = df['actual_lmp'] - df['actual_mcc']
    return df

def plot_lmp_forecast_vs_actual(
        aligned_df: pd.DataFrame,
        node: str,
        forecast_date: str,
        show_da: bool = False,
        show_range: bool = False
):
    """
    Plots forecasted vs actual LMPs (and optionally Day Ahead and prediction range) using an aligned DataFrame.

    Args:
        aligned_df (pd.DataFrame): DataFrame with 'forecast_datetime', 'actual_lmp', 'predicted_lmp', and 'pred_range_LMP'
                                   (optionally 'da_lmp').
        node (str): Node name (used for plot title).
        forecast_date (str): Forecast date (used for plot title).
        show_da (bool): If True, plot Day Ahead LMPs (only if column exists).
        show_range (bool): If True, plot predicted LMP ranges as box plots (only if column exists).
    """
    aligned_df = aligned_df.sort_values("forecast_datetime")

    # Build subtitle
    subtitle_parts = []
    if 'publication_datetime' in aligned_df.columns:
        pub_dt = pd.to_datetime(aligned_df['publication_datetime'].min()).strftime('%Y-%m-%d %H:%M %Z')
        subtitle_parts.append(f"Forecast run at {pub_dt} Central Time")
    if show_da and 'da_lmp' in aligned_df.columns:
        subtitle_parts.append("Includes Day Ahead")
    if show_range and 'pred_range_LMP' in aligned_df.columns:
        subtitle_parts.append("Includes Prediction Ranges")
    subtitle = "<br><sup>" + " | ".join(subtitle_parts) + "</sup>" if subtitle_parts else ""

    title_str = f"LMP Forecast vs Actual for '{node}' on {forecast_date.date()}"

    fig = go.Figure()

    # Predicted LMP
    fig.add_trace(go.Scatter(
        x=aligned_df["forecast_datetime"],
        y=aligned_df["predicted_lmp"],
        mode="lines+markers",
        name="Predicted LMP",
        line=dict(color="blue")
    ))

    # Actual LMP
    fig.add_trace(go.Scatter(
        x=aligned_df["forecast_datetime"],
        y=aligned_df["actual_lmp"],
        mode="lines+markers",
        name="Actual LMP",
        line=dict(color="orange")
    ))

    # Optional Day Ahead LMP
    if show_da and "da_lmp" in aligned_df.columns:
        fig.add_trace(go.Scatter(
            x=aligned_df["forecast_datetime"],
            y=aligned_df["da_lmp"],
            mode="lines+markers",
            name="Day Ahead LMP",
            line=dict(color="green")
        ))

    # Optional Range Box Plots
    if show_range and "pred_range_LMP" in aligned_df.columns:
        y_data = []
        x_data = []

        for i, row in aligned_df.iterrows():
            if isinstance(row["pred_range_LMP"], (list, tuple)):  # ensure it's iterable
                y_data.extend(row["pred_range_LMP"])
                x_data.extend([row["forecast_datetime"]] * len(row["pred_range_LMP"]))

        fig.add_trace(go.Box(
            y=y_data,
            x=x_data,
            name="LMP Forecast Range",
            marker=dict(color='rgba(70,130,180,0.5)'),
            line=dict(color='rgba(70,130,180,0.7)'),
            opacity=0.5,
            boxpoints=False,
        ))

    # Final layout
    fig.update_layout(
        title=dict(text=title_str + subtitle, x=0.5),
        xaxis_title="Hour",
        yaxis_title="LMP ($/MWh)",
        template="plotly_white",
        height=500,
        width=900
    )

    return fig

def lmp_plot_total(df):
    '''Creates three visuals.
    One with DA off and LMP range off
    One with DA on and LMP range off
    One with DA off and LMP range on
    Can be edited depending on the data to be displayed. These are examples'''
    # Plotting
    vis_w_da = plot_lmp_forecast_vs_actual(df, node_name, date, show_da=True)
    vis_no_da = plot_lmp_forecast_vs_actual(df, node_name, date, show_da=False)
    vis_w_range = plot_lmp_forecast_vs_actual(df, node_name, date, show_range=True)

    vis_no_da.show()
    vis_w_da.show()
    vis_w_range.show()

def calculate_mec_similarity(df):
    """
    Calculate similarity between predicted and actual MECs as a percentage based on
    mean absolute percentage error (MAPE), ignoring missing values.
    """
    df = df.dropna(subset=['predicted_mec', 'actual_mec'])
    df = df[df['actual_mec'].abs() > 1e-6]  # Avoid division by zero

    if df.empty:
        return None, "Not enough data to calculate similarity (missing or zero actual MECs)."

    mape = ((df['predicted_mec'] - df['actual_mec']).abs() / df['actual_mec'].abs()).mean()
    similarity = round((1 - mape) * 100, 2)
    similarity_text = f"Predicted and Actual MECs are approximately {similarity}% similar."
    return df, similarity_text

def plot_mec_comparison(df, similarity_text):
    """
    Plot predicted vs actual MEC using the merged DataFrame.
    Includes similarity score in the subtitle.
    """
    fig = go.Figure()

    fig.add_trace(go.Scatter(
        x=df['forecast_datetime'],
        y=df['predicted_mec'],
        mode='lines+markers',
        name='Predicted MEC',
        line=dict(color='blue')
    ))

    fig.add_trace(go.Scatter(
        x=df['forecast_datetime'],
        y=df['actual_mec'],
        mode='lines+markers',
        name='Actual MEC',
        line=dict(color='red')
    ))

    # Extract title info safely
    node = df['node_name'].iloc[0] if 'node_name' in df.columns else 'Unknown Node'
    forecast_date = df['forecast_datetime'].dt.date.min()
    publication_datetime = pd.to_datetime(df['publication_datetime'].min(), errors='coerce')

    if pd.isna(publication_datetime):
        pub_str = "Unknown time"
    else:
        pub_str = publication_datetime.strftime("%Y-%m-%d %H:%M")
        if publication_datetime.tzinfo is not None:
            pub_str += f" {publication_datetime.strftime('%Z')}"

    fig.update_layout(
        title=(f'MEC Forecast and Actual for {forecast_date}<br>'
               f'<sup>Forecast run on {pub_str}<br>'
               f'{similarity_text}<br>'
               f'All predicted MEC values are identical for the forecast date; '
               f'actual MEC values are calculated as actual LMP - MCC for node {node}.</sup>'),
        title_x=0.5,
        xaxis_title='Forecast Datetime',
        yaxis_title='MEC ($/MWh)',
        template='plotly_white'
    )

    fig.show()

def plot_mec_error(df):
    """
    Plot prediction error (predicted MEC - actual MEC) using the merged DataFrame.

    Args:
        df (pd.DataFrame): DataFrame with 'predicted_mec' and 'actual_mec'.
    """
    error = df['predicted_mec'] - df['actual_mec']

    # Extract info for title
    node = df['node_name'].iloc[0]
    forecast_date = df['forecast_datetime'].dt.date.min()
    publication_datetime = pd.to_datetime(df['publication_datetime'].min())

    fig = go.Figure()

    fig.add_trace(go.Scatter(
        x=df['forecast_datetime'],
        y=error,
        mode='lines+markers',
        name='MEC Forecast Error',
        line=dict(color='purple')
    ))

    fig.update_layout(
        title=(f'MEC Forecast Error for {forecast_date}<br>'
               f'<sup>Forecast run on {publication_datetime.strftime("%Y-%m-%d %H:%M %Z")}<br>'
               f'Error is calculated as (Predicted MEC - Actual MEC) for node {node}.</sup>'),
        title_x=0.5,
        xaxis_title='Forecast Datetime',
        yaxis_title='Error ($/MWh)',
        template='plotly_white'
    )

    fig.show()

def plot_mcc_forecast_vs_actual(df: pd.DataFrame):
    """
    Plots forecasted vs actual MCCs using the merged DataFrame.

    Args:
        df (pd.DataFrame): DataFrame with columns 'forecast_datetime', 'predicted_mcc', 'actual_mcc', 'publication_datetime', and 'node_name'
    """
    # Basic validation
    if df.empty or 'forecast_datetime' not in df.columns:
        raise ValueError("DataFrame is empty or missing required columns.")

    node = df['node_name'].iloc[0] if 'node_name' in df.columns else "Unknown Node"
    forecast_date = df['forecast_datetime'].dt.date.min()
    publication_datetime = pd.to_datetime(df['publication_datetime'].min(), errors='coerce')

    if pd.isna(publication_datetime):
        pub_str = "Unknown time"
    else:
        pub_str = publication_datetime.strftime("%Y-%m-%d %H:%M")
        if publication_datetime.tzinfo is not None:
            pub_str += f" {publication_datetime.strftime('%Z')}"

    fig = go.Figure()

    fig.add_trace(go.Scatter(
        x=df['forecast_datetime'],
        y=df['predicted_mcc'],
        mode='lines+markers',
        name='Predicted MCC',
        line=dict(color='blue')
    ))

    fig.add_trace(go.Scatter(
        x=df['forecast_datetime'],
        y=df['actual_mcc'],
        mode='lines+markers',
        name='Actual MCC',
        line=dict(color='red')
    ))

    fig.update_layout(
        title=(
            f"MCC Forecast and Actual for {forecast_date}<br>"
            f"<sup>Forecast run on {pub_str}<br>"
            f"Plotted for node {node}.</sup>"
        ),
        title_x=0.5,
        xaxis_title="Forecast Datetime",
        yaxis_title="MCC ($/MWh)",
        template="plotly_white"
    )

    fig.show()

def save_merged_df_to_csv(df: pd.DataFrame, filename_prefix: str = 'merged_forecast_actuals') -> str:
    """
    Saves the merged forecast vs actuals DataFrame to a timestamped CSV file in the current directory.

    Args:
        df (pd.DataFrame): The DataFrame to save.
        filename_prefix (str): Prefix for the filename.

    Returns:
        str: Filename of the saved CSV.
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{filename_prefix}_{timestamp}.csv"

    df.to_csv(filename, index=False)
    print(f"Saved to {filename}")
    return filename

### MAIN CODE ###
node_name = "senatewd_1"
date = dt.datetime(2025, 7, 25, tzinfo=dt.timezone.utc)
#################

merged_df = pull_and_merge_forecasts_and_actuals(node_name, date)
total_node_df = calculate_actual_mec(merged_df)

# LMP PLOT
lmp_plot_total(total_node_df)

# MEC PLOT
df_with_similarity, similarity_text = calculate_mec_similarity(total_node_df)
plot_mec_comparison(df_with_similarity, similarity_text)
plot_mec_error(df_with_similarity)

# MCC PLOT
plot_mcc_forecast_vs_actual(total_node_df)

# SAVE TO CSV
save_merged_df_to_csv(total_node_df)