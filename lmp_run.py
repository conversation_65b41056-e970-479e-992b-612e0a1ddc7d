# run_script.py

from lmp_main import export_multiple_nodes_to_csv

if __name__ == "__main__":
   
    # Simple input
    publication_datetime = "2025-07-23 05:00"  
    nodes_list = ['aeec', 'ae_rn', 'aguayo_unit1']

    # Create output filename
    datetime_for_filename = publication_datetime.replace(" ", "_").replace(":", "-")
    output_file = f"multiple_nodes_{datetime_for_filename}_forecast.csv"

    # Run export
    export_multiple_nodes_to_csv(nodes_list, publication_datetime, output_file)
