# main_script.py

import requests
import pandas as pd
from datetime import datetime
import pytz
from urllib.parse import quote


def convert_datetime_to_iso(datetime_str):
    """
    Convert datetime from 'YYYY-MM-DD HH:MM' format to ISO format with timezone
    like '2025-07-25T14:00:00-05:00'
    """
    print(f"Converting datetime: {datetime_str}")
    dt = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M')
    utc_dt = pytz.utc.localize(dt)
    iso_format = utc_dt.isoformat()

    print(f"Converted to: {iso_format}")
    return iso_format


def fetch_forecast_data(node, pub_datetime):
    """
    Fetch forecast data from API for a given node and publication datetime.
    """
    if 'T' not in pub_datetime:
        pub_datetime = convert_datetime_to_iso(pub_datetime)

    encoded_datetime = quote(pub_datetime)
    url = f"https://venom.int.woodmac.com/api/v1/Forecast/Prediction?node={node}&publicationDateTime={encoded_datetime}"
    print(f"Making request for node {node}: {url}")

    response = requests.get(url)
    print(f"Response status for {node}: {response.status_code}")
    
    if response.status_code != 200:
        print(f"Response content for {node}: {response.text}")
        raise Exception(f"Failed to fetch data for {node}: {response.status_code} - {response.text}")
    
    return response.json()


def export_multiple_nodes_to_csv(nodes, pub_datetime, output_path):
    """
    Export forecast data for multiple nodes to a CSV file.
    """
    all_rows = []
    successful_nodes = []
    failed_nodes = []

    print(f"Processing {len(nodes)} nodes...")

    for i, node in enumerate(nodes, 1):
        print(f"\n[{i}/{len(nodes)}] Processing node: {node}")
        try:
            data = fetch_forecast_data(node, pub_datetime)
            predictions = data['Predictions']
            node_rows = 0

            for ts_str, values in predictions.items():
                ts = datetime.fromisoformat(ts_str)
                lmp = values.get('lmp')
                pred_range = values.get('pred_range_LMP', [])
                date_str = ts.strftime('%Y-%m-%d')
                hour = ts.hour

                for pred in pred_range:
                    all_rows.append({
                        'NODE': node,
                        'DATE': date_str,
                        'TIME/HOUR': hour,
                        'HOURLY LMP': lmp,
                        'PREDICTED RANGE LMP': pred
                    })
                    node_rows += 1

            successful_nodes.append(node)
            print(f"✓ Successfully processed {node} - {node_rows} rows added")

        except Exception as e:
            failed_nodes.append(node)
            print(f"✗ Error processing node {node}: {e}")
            continue

    if all_rows:
        df = pd.DataFrame(all_rows)
        df.to_csv(output_path, index=False)
        print(f"\n=== EXPORT SUMMARY ===")
        print(f"Total rows exported: {len(all_rows)}")
        print(f"Successful nodes ({len(successful_nodes)}): {', '.join(successful_nodes)}")
        if failed_nodes:
            print(f"Failed nodes ({len(failed_nodes)}): {', '.join(failed_nodes)}")
        print(f"Output file: {output_path}")
        print(f"======================")
    else:
        print("No data was successfully retrieved from any nodes.")
