{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9b0c75f0", "metadata": {}, "outputs": [], "source": ["import dash\n", "from dash import dcc, html\n", "import plotly.express as px\n", "import pandas as pd\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "5876d072", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8050/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x2d70a13fcb0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Sample data\n", "df = pd.DataFrame({\n", "    'Category': ['A', 'B', 'C', 'D'],\n", "    'Value': [10, 20, 15, 25]\n", "})\n", "\n", "# Initialize the Dash app\n", "app = dash.Dash(__name__)\n", "\n", "# App layout\n", "app.layout = html.Div(children=[\n", "    html.H1(\"Simple Dash Dashboard\", style={'textAlign': 'center'}),\n", "\n", "    dcc.Dropdown(\n", "        id='chart-type',\n", "        options=[\n", "            {'label': 'Bar Chart', 'value': 'bar'},\n", "            {'label': 'Line Chart', 'value': 'line'}\n", "        ],\n", "        value='bar',\n", "        style={'width': '50%', 'margin': '0 auto'}\n", "    ),\n", "\n", "    dcc.Graph(id='chart')\n", "])\n", "\n", "# Callback to update the graph\n", "@app.callback(\n", "    dash.dependencies.Output('chart', 'figure'),\n", "    [dash.dependencies.Input('chart-type', 'value')]\n", ")\n", "def update_chart(chart_type):\n", "    if chart_type == 'bar':\n", "        fig = px.bar(df, x='Category', y='Value', title=\"Bar Chart\")\n", "    else:\n", "        fig = px.line(df, x='Category', y='Value', title=\"Line Chart\")\n", "    return fig\n", "\n", "# Run the app\n", "if __name__ == '__main__':\n", "    app.run(debug=True)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d894fcf6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "152f4b73", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.9 (venom)", "language": "python", "name": "venom_py39"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}