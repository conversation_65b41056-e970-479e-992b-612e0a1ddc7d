# interactive_dashboard.py

import pandas as pd
import numpy as np
import dash
from dash import dcc, html, Input, Output
import plotly.express as px

# Generate and save mock data
def export_multiple_nodes_to_csv(nodes, start_date, output_file):
    timestamps = pd.date_range(start=start_date, periods=24, freq='h')
    all_data = []

    for node in nodes:
        df = pd.DataFrame({
            'Timestamp': timestamps,
            'Node': node,
            'Forecasted_LMP': np.round(20 + 10 * np.random.rand(24), 2)
        })
        all_data.append(df)

    result = pd.concat(all_data)
    result.to_csv(output_file, index=False)
    return output_file

# Setup
default_date = "2025-07-23"
nodes_list = ['aeec', 'ae_rn', 'aguayo_unit1']
output_file = "forecast_data.csv"
export_multiple_nodes_to_csv(nodes_list, default_date, output_file)

# Read data
df = pd.read_csv(output_file)

# Create Dash app
app = dash.Dash(__name__)
app.title = "Forecast Dashboard"

app.layout = html.Div([
    html.H1("Forecasted LMP Dashboard"),

    html.Div([
        html.Div([
            html.Label("Select Date:"),
            dcc.DatePickerSingle(
                id='date-picker',
                date=default_date,
                display_format='YYYY-MM-DD',
                style={'margin-bottom': '10px'}
            )
        ], style={'width': '48%', 'display': 'inline-block'}),

        html.Div([
            html.Label("Select Node(s):"),
            dcc.Dropdown(
                id='node-dropdown',
                options=[{"label": node, "value": node} for node in df["Node"].unique()],
                value=[df["Node"].unique()[0]],  # default value
                multi=True
            )
        ], style={'width': '48%', 'float': 'right', 'display': 'inline-block'})
    ]),

    dcc.Graph(id="lmp-graph")
])

# Callback to update graph based on selected nodes and date
@app.callback(
    Output("lmp-graph", "figure"),
    [Input("node-dropdown", "value"),
     Input("date-picker", "date")]
)
def update_graph(selected_nodes, selected_date):
    # Generate new data for the selected date
    temp_file = "temp_forecast_data.csv"
    export_multiple_nodes_to_csv(nodes_list, selected_date, temp_file)
    temp_df = pd.read_csv(temp_file)

    # Filter by selected nodes
    filtered_df = temp_df[temp_df["Node"].isin(selected_nodes)]
    fig = px.line(filtered_df, x="Timestamp", y="Forecasted_LMP", color="Node",
                  title=f"Forecasted LMP by Node - {selected_date}")
    return fig

# Run server
if __name__ == "__main__":
    app.run(debug=True)
