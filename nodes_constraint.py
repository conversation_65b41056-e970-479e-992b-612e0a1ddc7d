import requests
import pandas as pd
from datetime import datetime
import os

# --- Constants ---
API_URL = "https://venom.iuat.woodmac.com/api/v1/Forecast/ConstraintPrediction"
HEADERS = {
    "accept": "application/json",
    "Authorization": "37bb8490-66c2-11f0-aef1-cb0a1b939326"
}

# --- Fetch data ---
def fetch_constraint_data(publication_datetime: str):
    try:
        url = f"{API_URL}?publicationDateTime={publication_datetime}"
        print(" Requesting:", url)
        response = requests.get(url, headers=HEADERS)
        response.raise_for_status()
        data = response.json()
        print(f" Received JSON with keys: {list(data.keys())}")

        predictions = data.get("Predictions")
        if not isinstance(predictions, dict) or not predictions:
            print(" 'Predictions' not found or not a valid dictionary.")
            return publication_datetime, []

        rows = []
        for timestamp, values in predictions.items():
            try:
                dt = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                hour_ending = dt.hour + 1
                forecast_date = dt.date()
            except Exception as e:
                print(f" Failed to parse timestamp '{timestamp}': {e}")
                continue

            if not isinstance(values, dict):
                continue

            for key, shadow_price in values.items():
                if not isinstance(shadow_price, (int, float)):
                    continue
                rows.append({
                    "ConstraintKey": key,
                    "Hour": hour_ending,
                    "ShadowPrice": float(shadow_price),
                    "ForecastDate": str(forecast_date)
                })

        return publication_datetime, rows

    except Exception as e:
        print(f" Error fetching/parsing data: {e}")
        return publication_datetime, []

# --- Parse constraint key ---
def parse_constraint_key(key: str):
    parts = key.split("#") if key else [""] * 6
    while len(parts) < 6:
        parts.append("")
    return {
        "Constraint": parts[0],
        "Contingency": parts[1],
        "FromStation": parts[2],
        "FromKV": parts[3],
        "ToStation": parts[4],
        "ToKV": parts[5]
    }

# --- Build summary table ---
def build_constraint_table(raw_rows):
    if not raw_rows:
        return pd.DataFrame()

    expanded_rows = []
    for row in raw_rows:
        parsed = parse_constraint_key(row["ConstraintKey"])
        parsed.update({
            "Hour": row["Hour"],
            "ShadowPrice": row["ShadowPrice"],
            "ForecastDate": row["ForecastDate"]
        })
        expanded_rows.append(parsed)

    df = pd.DataFrame(expanded_rows)
    if df.empty:
        return df

    pivot = df.pivot_table(
        index=["Constraint", "Contingency", "FromStation", "FromKV", "ToStation", "ToKV", "ForecastDate"],
        columns="Hour",
        values="ShadowPrice",
        fill_value=0.0,
        aggfunc='first'
    ).reset_index()

    hour_columns = sorted([col for col in pivot.columns if isinstance(col, int)])

    pivot["Total"] = pivot[hour_columns].sum(axis=1)

    ordered_cols = [
        "ForecastDate", "Constraint", "Contingency", "FromStation", "FromKV", "ToStation", "ToKV", "Total"
    ] + hour_columns

    return pivot[ordered_cols].sort_values(by="Total", ascending=False).reset_index(drop=True)

# --- Export with heatmap ---
def export_to_excel(df: pd.DataFrame, publication_datetime: str):
    if df.empty:
        print(" Nothing to export.")
        return

    safe_datetime = publication_datetime.replace(":", "-")
    file_name = f"ConstraintSummary_PubDate_{safe_datetime}.xlsx"

    try:
        with pd.ExcelWriter(file_name, engine="xlsxwriter") as writer:
            # Start data from row 4 to leave space for title
            df.to_excel(writer, index=False, sheet_name="Summary", startrow=3)
            workbook = writer.book
            worksheet = writer.sheets["Summary"]

            # --- FORMATTING STYLES ---
            # Title format
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'font_name': 'Calibri',
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#0A38FF',  # Dark blue background
                'font_color': 'white',
                'border': 2,
                'border_color': '#000000'
            })

            # Header format
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 11,
                'font_name': 'Calibri',
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#2f5f8f',  # Medium blue
                'font_color': 'white',
                'border': 2,
                'border_color': '#000000',
                'text_wrap': True
            })

            # Data cell format with borders
            data_format = workbook.add_format({
                'font_size': 10,
                'font_name': 'Calibri',
                'border': 1,
                'border_color': '#000000',
                'align': 'center',
                'valign': 'vcenter'
            })

            # --- ADD TITLE ---
            # Parse datetime for display
            try:
                dt_obj = datetime.fromisoformat(publication_datetime.replace("Z", "+00:00"))
                formatted_date = dt_obj.strftime("%B %d, %Y at %I:%M %p")
            except:
                formatted_date = publication_datetime

            title_text = f"Constraint Summary - Generated for {formatted_date}"

            # Merge cells for title and add title
            worksheet.merge_range('A1:H2', title_text, title_format)

            # --- APPLY HEADER FORMATTING ---
            for col_num, column_name in enumerate(df.columns):
                worksheet.write(3, col_num, column_name, header_format)

            # --- APPLY DATA FORMATTING WITH BORDERS ---
            for row_num in range(len(df)):
                for col_num in range(len(df.columns)):
                    cell_value = df.iloc[row_num, col_num]
                    worksheet.write(row_num + 4, col_num, cell_value, data_format)

            # --- CALCULATE THRESHOLDS FOR COBALT BLUE & GREY GRADIENT ---
            min_val = df["Total"].min()
            avg_val = df["Total"].mean()
            max_val = df["Total"].max()

            # Define cobalt blue to grey to white gradient colors
            colors = {
                'dark_cobalt': '#003f7f',      # Dark cobalt blue for max values
                'medium_cobalt': '#1a5490',    # Medium cobalt blue
                'light_cobalt': '#4d79a4',     # Light cobalt blue
                'dark_grey': '#666666',        # Dark grey for average values
                'medium_grey': '#999999',      # Medium grey
                'light_grey': '#cccccc',       # Light grey
                'white': '#ffffff'             # White for min values
            }

            # --- APPLY GRADIENT TO ENTIRE WORKBOOK ---
            # Get Total column letter for formula references
            total_col_index = df.columns.get_loc("Total")
            total_col_letter = chr(ord('A') + total_col_index)

            # Apply gradient to ALL data columns (entire workbook)
            for col_idx in range(len(df.columns)):
                col_letter = chr(ord('A') + col_idx)

                # Dark Cobalt Blue - Top 10% (closest to max)
                threshold_90 = min_val + (max_val - min_val) * 0.9
                worksheet.conditional_format(
                    4, col_idx, len(df) + 3, col_idx,
                    {
                        "type": "formula",
                        "criteria": f"${total_col_letter}5>={threshold_90}",
                        "format": workbook.add_format({
                            'bg_color': colors['dark_cobalt'],
                            'font_color': 'white',
                            'bold': True,
                            'border': 1,
                            'border_color': '#000000'
                        })
                    }
                )

                # Medium Cobalt Blue - 70-90%
                threshold_70 = min_val + (max_val - min_val) * 0.7
                worksheet.conditional_format(
                    4, col_idx, len(df) + 3, col_idx,
                    {
                        "type": "formula",
                        "criteria": f"${total_col_letter}5>={threshold_70}",
                        "format": workbook.add_format({
                            'bg_color': colors['medium_cobalt'],
                            'font_color': 'white',
                            'border': 1,
                            'border_color': '#000000'
                        })
                    }
                )

                # Light Cobalt Blue - 50-70%
                threshold_50 = min_val + (max_val - min_val) * 0.5
                worksheet.conditional_format(
                    4, col_idx, len(df) + 3, col_idx,
                    {
                        "type": "formula",
                        "criteria": f"${total_col_letter}5>={threshold_50}",
                        "format": workbook.add_format({
                            'bg_color': colors['light_cobalt'],
                            'font_color': 'white',
                            'border': 1,
                            'border_color': '#000000'
                        })
                    }
                )

                # Dark Grey - 30-50% (around average)
                threshold_30 = min_val + (max_val - min_val) * 0.3
                worksheet.conditional_format(
                    4, col_idx, len(df) + 3, col_idx,
                    {
                        "type": "formula",
                        "criteria": f"${total_col_letter}5>={threshold_30}",
                        "format": workbook.add_format({
                            'bg_color': colors['dark_grey'],
                            'font_color': 'white',
                            'border': 1,
                            'border_color': '#000000'
                        })
                    }
                )

                # Medium Grey - 15-30%
                threshold_15 = min_val + (max_val - min_val) * 0.15
                worksheet.conditional_format(
                    4, col_idx, len(df) + 3, col_idx,
                    {
                        "type": "formula",
                        "criteria": f"${total_col_letter}5>={threshold_15}",
                        "format": workbook.add_format({
                            'bg_color': colors['medium_grey'],
                            'font_color': 'black',
                            'border': 1,
                            'border_color': '#000000'
                        })
                    }
                )

                # Light Grey - 5-15%
                threshold_5 = min_val + (max_val - min_val) * 0.05
                worksheet.conditional_format(
                    4, col_idx, len(df) + 3, col_idx,
                    {
                        "type": "formula",
                        "criteria": f"${total_col_letter}5>={threshold_5}",
                        "format": workbook.add_format({
                            'bg_color': colors['light_grey'],
                            'font_color': 'black',
                            'border': 1,
                            'border_color': '#000000'
                        })
                    }
                )

                # White background for lowest values (0-5%) - already default

            # --- COLUMN WIDTHS ---
            column_widths = {
                'ForecastDate': 12,
                'Constraint': 20,
                'Contingency': 15,
                'FromStation': 15,
                'FromKV': 8,
                'ToStation': 15,
                'ToKV': 8,
                'Total': 10
            }

            for col_num, column_name in enumerate(df.columns):
                if column_name in column_widths:
                    worksheet.set_column(col_num, col_num, column_widths[column_name])
                elif isinstance(column_name, int):  # Hour columns
                    worksheet.set_column(col_num, col_num, 6)
                else:
                    worksheet.set_column(col_num, col_num, 12)

            # Set row heights
            worksheet.set_row(0, 25)  # Title row height
            worksheet.set_row(1, 25)  # Title row height
            worksheet.set_row(3, 20)  # Header row height

        print(f" Exported with enhanced heatmap to {os.path.abspath(file_name)}")
    except Exception as e:
        print(f" Failed to write Excel file: {e}")

# --- Main function ---
def generate_summary(publication_datetime: str):
    print(f" Generating summary for publication datetime: {publication_datetime}")

    try:
        dt = datetime.strptime(publication_datetime, "%Y-%m-%d %H:%M")
        publication_datetime = dt.strftime("%Y-%m-%dT%H:%M:%S-05:00")
    except ValueError:
        print(" Please enter datetime in format: YYYY-MM-DD HH:MM (e.g. 2025-08-06 06:00)")
        return

    publication_datetime, raw_data = fetch_constraint_data(publication_datetime)

    if not raw_data:
        print(" No data retrieved.")
        return

    print(f" Retrieved {len(raw_data)} records across 24 hours.")
    summary_df = build_constraint_table(raw_data)
    print(f" Summary created: {len(summary_df)} unique constraints.")
    export_to_excel(summary_df, publication_datetime)
